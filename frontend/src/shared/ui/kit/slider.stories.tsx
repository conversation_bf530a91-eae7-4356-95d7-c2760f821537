import type { Meta, StoryObj } from '@storybook/react-vite'

import { Slider } from './slider'
import { useState } from 'react'
import clsx from 'clsx'

const meta = {
    component: Slider,
} satisfies Meta<typeof Slider>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: () => {
        const [value, setValue] = useState([1])
        console.log(value)
        const ramExpansions = [
            'ЧерепахаЧерепахаЧерепаха',
            'ЧерепахаЧерепахаЧерепаха',
            'ЧерепахаЧерепахаЧерепаха',
        ]

        return (
            <>
                <Slider
                    defaultValue={[1]}
                    max={2}
                    step={1}
                    value={value}
                    onValueChange={setValue}
                />
                <div className="mt-4 flex items-center justify-around text-muted-foreground text-xs">
                    {ramExpansions.map((expansion, i) => (
                        <span
                            key={i}
                            className={clsx(i !== value[0] && 'text-red-300')}
                        >
                            {expansion}
                        </span>
                    ))}
                </div>
            </>
        )
    },
}
